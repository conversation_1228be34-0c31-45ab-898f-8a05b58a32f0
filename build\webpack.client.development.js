const path = require('path');
const { DefinePlugin } = require("webpack");
const { merge } = require("webpack-merge");
const ESLintPlugin = require('eslint-webpack-plugin');
const StylelintWebpackPlugin = require('stylelint-webpack-plugin');

const baseConfig =  require('./webpack.base');
const VueSSRClientPlugin = require('./plugins/vue-ssr-client-plugin');


const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = merge(baseConfig, {
  mode: 'development',
  target: ['web', 'es5'],
  entry: {
    app: resolve('../src/entry-client.ts'),
  },
  plugins: [
    new DefinePlugin({
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
    }),
    new ESLintPlugin({
      extensions: ['js', 'ts', 'vue'],
    }),
    new StylelintWebpackPlugin({
      files: ['**/*.{vue,css,less}'],
    }),
    new VueSSRClientPlugin(),
  ],
});
