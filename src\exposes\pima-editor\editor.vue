<script>
import { defineComponent, reactive, toRefs, unref } from 'vue';
import { VueUeditorWrap } from 'vue-ueditor-wrap';
import URI from 'urijs';
// eslint-disable-next-line import/no-extraneous-dependencies
import merge from 'merge';
import { UEDITOR_PATH, UEDITOR_VERSION } from '@/config/index';
import { insertWordPlugin } from './insert-word-plugin';


export default defineComponent({
  name: 'PimaEditor',

  components: {
    VueUeditorWrap,
  },

  props: {
    config: {
      type: Object,
      default() {
        return null;
      },
    },

    height: {
      type: Number,
      default: 670,
    },

    getToken: {
      type: Function,
      default() {
        return () => {};
      },
    },

    insertWordPluginButtonIndex: {
      type: Number,
      default: 26,
    },
  },

  emits: [
    'before-init',
    'ready',
  ],

  setup(props, { emit }) {
    const { config, getToken, height, insertWordPluginButtonIndex } = toRefs(props);

    function serverUrl() {
      const hasConfig = (unref(config) && 'uploadImageApiUrl' in unref(config));
      const uploadImageApiUrl = hasConfig ? unref(config).uploadImageApiUrl : '';
      const u = URI(uploadImageApiUrl);
      u.setQuery('Authorization', unref(getToken)());
      const s = u.href();
      return s;
    }

    function ueditorHomeUrl() {
      const hasConfig = (unref(config) && 'staticResourcesBaseUrl' in unref(config));
      let baseUrl = hasConfig ? unref(config).staticResourcesBaseUrl : process.env.STATIC_RESOURCES_BASE_URL;
      if (baseUrl[baseUrl.length - 1] !== '/') {
        baseUrl += '/';
      }

      return `${baseUrl}${UEDITOR_PATH}/${UEDITOR_VERSION}/`;
    }

    function currentConfig() {
      if (unref(config) !== null && typeof unref(config) === 'object') {
        const currConfig = { ...unref(config) };
        // 以下两个配置为自定义配置，需要从配置中删除
        delete currConfig.uploadImageApiUrl;
        delete currConfig.staticResourcesBaseUrl;
        if (Object.keys(currConfig).length > 0) {
          return currConfig;
        }
      }

      return null;
    }

    function createConfig() {
      const defaultConfig = {
        // 配置说明文档 https://fex.baidu.com/ueditor/#start-config
        serverUrl: serverUrl(),
        UEDITOR_HOME_URL: ueditorHomeUrl(),
        autoHeightEnabled: true,
        initialFrameHeight: unref(height),
        // scaleEnabled: true,
        initialFrameWidth: '100%',
        autoFloatEnabled: false,
        enableAutoSave: false,
        enablePasteUpload: true,
        toolbars: [
          [
            // 'fullscreen', 'source', '|', // 全屏存在问题，待解决
            'source', '|',
            'undo', 'redo', '|',
            'bold', 'italic', 'strikethrough', 'pasteplain', '|',
            'fontsize', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', '|',
            'justifyleft', 'justifycenter', 'justifyright', '|',
            'link', 'unlink', '|',
            'simpleupload', 'insertimage', 'insertword', 'insertvideo', '|',
            'inserttable', 'horizontal',
          ],
        ],
        // 部分默认样式已不可用，故重新定认默认选项
        insertorderedlist: {
          decimal: '1,2,3...',
          'lower-alpha': '', // 'a,b,c...'
          'lower-roman': '', // 'i,ii,iii...'
          'upper-alpha': '', // 'A,B,C'
          'upper-roman': '', // 'I,II,III...'
        },
        allowDivTransToP: false,
        elementPathEnabled: false,
        wordCount: false,
        initialStyle: '.pima-editor{overflow-y:visible}',
        // 调整zIndex
        // iView抽屉的zIndex为1000，需比这个值大
        // 但如果编辑器直接在界面上不在抽屉，则有可能受共用头部影响，建议调整zIndex到200以下
        zIndex: 0,
      };

      const cf = merge(defaultConfig, currentConfig());

      return cf;
    }

    const ueditorConfig = reactive(createConfig());

    function needInsertWordPlugin() {
      if ('toolbars' in ueditorConfig && Array.isArray(ueditorConfig.toolbars)) {
        for (let i = 0; i < ueditorConfig.toolbars.length; i += 1) {
          if (Array.isArray(ueditorConfig.toolbars[i])) {
            if (ueditorConfig.toolbars[i].includes('insertword')) {
              return true;
            }
          }
        }
      }

      return false;
    }

    function onBeforeInit() {
      if (needInsertWordPlugin()) {
        /* index 指定添加到工具栏上的那个位置，默认时追加到最后,editorId 指定这个UI是那个编辑器实例上的，默认是页面上所有的编辑器都会添加这个按钮 */
        window.UE.registerUI('insertword', insertWordPlugin, unref(insertWordPluginButtonIndex));
      }

      emit('before-init');
    }

    function onReady(editorInstance) {
      // 处理 ctrl + enter 会自动提交表单的问题
      Object.assign(editorInstance.shortcutkeys, { autosubmit: '' });

      emit('ready', editorInstance);
    }

    return {
      ueditorConfig,
      onBeforeInit,
      onReady,
    };
  },
});
</script>


<template>
  <div class="pima-editor">
    <VueUeditorWrap
      :config="ueditorConfig"
      v-bind="$attrs"
      @before-init="onBeforeInit"
      @ready="onReady"
    />
  </div>
</template>


<style lang="less" scoped>
.pima-editor {
  :deep(.edui-default .edui-toolbar) {
    line-height: 20px;
  }
}
</style>
