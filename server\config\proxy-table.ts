/* eslint-disable no-undef */
/* eslint-disable import/extensions */
const apis = require('./index');

module.exports = function createProxyTable(apis) {
  const urls = {
    [apis.LOCAL_BDC_CORE_API_BASE_URL]: process.env.BDC_CORE_API_BASE_URL,
    [apis.LOCAL_STATIC_RESOURCES_BASE_URL]: process.env.STATIC_RESOURCES_BASE_URL,

    [apis.LOCAL_WORKFLOW_API_BASE_URL]: process.env.WORKFLOW_API_BASE_URL,
    [apis.LOCAL_BDC_ARCH_API_BASE_URL]: process.env.BDC_ARCH_API_BASE_URL,
    [apis.LOCAL_BDC_AUTH_API_BASE_URL]: process.env.BDC_AUTH_API_BASE_URL,
    [apis.LOCAL_BDC_SERVICE_API_BASE_URL]: process.env.BDC_SERVICE_API_BASE_URL,
    [apis.LOCAL_BDC_IMPORT_API_BASE_URL]: process.env.BDC_IMPORT_API_BASE_URL,
    [apis.LOCAL_BDC_EXPORT_API_BASE_URL]: process.env.BDC_EXPORT_API_BASE_URL,
  };

  const config = {};
  Object.entries(urls).forEach(([localUrl, remoteUrl]) => {
    Object.assign(config, {
      [localUrl]: {
        target: remoteUrl,
        pathRewrite: {
          [localUrl]: '',
        },
        changeOrigin: true,
        xfwd: true,
        logLevel: 'debug',
      },
    });
  });

  return config;
};
