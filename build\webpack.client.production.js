const path = require('path');
const { merge } = require("webpack-merge");
// const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

const baseConfig =  require('./webpack.base');
const VueSSRClientPlugin = require('./plugins/vue-ssr-client-plugin');


const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = merge(baseConfig, {
  mode: 'production',
  target: ['web', 'es5'],
  entry: {
    app: resolve('../src/entry-client.ts'),
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          format: {
            comments: false,
          },
        },
        extractComments: false,
      }),
    ],
  },
  plugins: [
    new VueSSRClientPlugin(),
  ],
  resolve: {
    alias: {
      // issue: https://github.com/intlify/vue-i18n-next/issues/219
      'vue-i18n': resolve('../node_modules/vue-i18n/dist/vue-i18n.esm-browser.prod.js'),
    },
  },
});
