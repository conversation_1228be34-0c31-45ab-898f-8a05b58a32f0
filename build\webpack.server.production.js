const path = require('path');
const { merge } = require("webpack-merge");
const nodeExternals = require("webpack-node-externals");

const VueServerPlugin = require('./plugins/vue-ssr-server-plugin');
const baseConfig = require('./webpack.base');


const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = merge(baseConfig, {
  mode: 'production',
  target: 'node',
  entry: resolve('../src/entry-server.ts'),
  output: {
    filename: 'server-bundle.js',
    libraryTarget: 'commonjs2',
    path: resolve('../dist'),
  },
  externalsPresets: { node: true },
  externals: nodeExternals({
    allowlist: [/\.css$/, 'webpack/hot/dev-server', 'pimaRemoteEditor'],
  }),
  plugins: [
    new VueServerPlugin(),
  ],
});
