{"name": "pima-remote-editor-vue3-nodejs", "version": "1.0.0", "private": true, "description": "PIMA远程编辑器Vue3版本", "author": "<EMAIL>", "license": "ISC", "scripts": {"build": "rimraf ./dist && yarn run build:client && yarn run build:server", "build:client": "cross-env NODE_ENV=production webpack --config ./build/webpack.client.production.js", "build:server": "cross-env NODE_ENV=production webpack --config ./build/webpack.server.production.js", "server": "cross-env NODE_ENV=production ts-node ./server/server.ts", "dev": "ts-node ./server/server.ts", "analyz": "cross-env report=1 yarn run build"}, "dependencies": {"@types/lodash": "^4.17.13", "b64-to-blob": "^1.2.19", "body-parser": "^1.20.2", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "core-js": "^3.39.0", "dotenv": "^16.4.5", "dotenv-webpack": "^8.1.0", "express": "^4.21.2", "express-session": "^1.18.1", "ioredis": "^5.4.1", "lru-cache": "^10.4.3", "mammoth": "^1.4.21", "merge": "^2.1.1", "morgan": "^1.10.0", "nodemon": "^3.0.1", "regenerator-runtime": "^0.14.1", "urijs": "^1.19.11", "vue": "^3.5.22", "vue-i18n": "^10.0.5", "vue-ueditor-wrap": "^3.0.8"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^16.18.119", "@types/urijs": "^1.19.25", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^5.0.1", "esbuild-loader": "^4.2.2", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.3", "eslint-import-resolver-webpack": "^0.13.9", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^9.31.0", "eslint-webpack-plugin": "^4.2.0", "html-webpack-plugin": "^5.6.3", "less": "^4.2.0", "less-loader": "^11.1.4", "memory-fs": "^0.5.0", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "postcss-loader": "^7.3.4", "postcss-preset-env": "^9.6.0", "rimraf": "^5.0.5", "style-loader": "^3.3.4", "stylelint": "^15.11.0", "stylelint-config-rational-order-fix": "^0.1.9", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-less": "^2.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-less": "^2.0.0", "stylelint-order": "^6.0.4", "stylelint-webpack-plugin": "^4.1.1", "terser-webpack-plugin": "^5.3.10", "thread-loader": "^4.0.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "typescript": "^5.6.3", "vue-eslint-parser": "^9.4.3", "vue-loader": "^17.4.2", "webpack": "^5.96.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-middleware": "^6.1.3", "webpack-federation-module-id-plugin": "^1.0.0", "webpack-federation-stats-plugin": "^1.0.2", "webpack-hot-middleware": "^2.26.1", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.10.0", "webpack-node-externals": "^3.0.0"}, "resolutions": {"braces": "^3.0.3", "stylelint-config-standard": "^34.0.0", "stylelint-config-recommended": "^13.0.0"}, "engines": {"node": ">=16.18.0"}}