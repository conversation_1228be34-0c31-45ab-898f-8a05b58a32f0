<template>
  <!-- SSR需要在根组件带上id="app"做挂载 -->
  <div id="app">
    <PimaEditor
      :upload-image-api-url="uploadImageApiUrl()"
      :get-token="getToken"
    />
  </div>
</template>


<script>
import { defineComponent } from 'vue';

import PimaEditor from '@/exposes/pima-editor';


export default defineComponent({
  name: 'App',

  components: {
    PimaEditor,
  },

  setup() {
    function uploadImageApiUrl() {
      return 'http://wmhelpcenter.doocom.net:8081/attachments/editor/upload';
    }

    function getToken() {
      return 'new_9d410088-4474-475c-8b9d-5ea517bd6faa';
    }

    return {
      uploadImageApiUrl,
      getToken,
    };
  },
});
</script>


<style lang="less">
body {
  margin: 0;
  padding: 0;
}
</style>
