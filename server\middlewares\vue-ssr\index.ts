module.exports = (app, options) => {
  const fs = require('fs');
  // eslint-disable-next-line import/no-extraneous-dependencies
  const { renderToString } = require('@vue/server-renderer');

  const compileModule = require('./compile-module');
  const TemplateRenderer = require('./template-renderer');
  const setupDevServer = require('./setup-dev-server');
  const cache = require('./cache')();

  let renderer;
  let template;
  let clientManifest;

  async function render(req, res) {
    if (!renderer) {
      return {
        status: 200,
        type: 'text/html',
        body: 'Waiting for compilation... refresh in a moment.',
      };
    }

    const context = {
      url: req.url,
      query: req.query,
      ...options,
      ...res.locals,
    };
    const vueApp = await renderer(context);
    const content = await renderToString(vueApp, {
      url: req.url,
      query: req.query,
      ...res.locals,
    });

    const templateRenderer = new TemplateRenderer({
      template,
      inject: true,
      clientManifest,
      shouldPreload: () => false,
      shouldPrefetch: () => false,
    });

    const html = templateRenderer.render(content, context);
    return {
      status: 200,
      type: 'text/html',
      body: html,
    };
  }

  function getRenderer() {
    const {
      isProd,
      templatePath,
      webpackServerConfig,
      vueSSRServerBundlePath,
      vueSSRClientManifestPath,
    } = options;
    if (isProd) {
      const serverManifest = JSON.parse(fs.readFileSync(vueSSRServerBundlePath, 'utf-8'));
      const evaluate = compileModule(serverManifest.files, false, false);
      renderer = evaluate(webpackServerConfig.output.filename, global);
      template = fs.readFileSync(templatePath, 'utf-8');
      clientManifest = JSON.parse(fs.readFileSync(vueSSRClientManifestPath, 'utf-8'));
    } else {
      setupDevServer(app, options, ({ renderer: rd, template: tl, clientManifest: cm }) => {
        renderer = rd;
        template = tl;
        clientManifest = cm;
      });
    }
  }

  async function vueSSRMiddleware(req, res, next) {
    const cacheable = cache.isCacheable(req);
    const cacheKey = cache.genCacheKey(req, res);
    if (cacheable) {
      const hit = cache.getCachedItem(cacheKey);
      if (hit) {
        res.type('text/html');
        res.send(hit);
        next();
        return;
      }
    }

    try {
      const { status, type, body } = await render(req, res);
      res.status(status);
      res.type(type);
      res.send(body);
      next();
    } catch (error) {
      next(error);
    }
  }

  getRenderer();

  return vueSSRMiddleware;
};
