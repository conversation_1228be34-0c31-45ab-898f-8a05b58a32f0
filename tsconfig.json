{
  "compilerOptions": {
    "target": "ES5",
    "module": "CommonJS",
    "strict": false,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "Node",
    "experimentalDecorators": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "~/*": ["node_modules/*"],
    },
    "lib": ["es2015", "esnext", "dom", "dom.iterable", "scripthost"]
  },
  "include": [
    "server/**/*.ts",
    "src/**/*.ts",
    "src/**/*.vue",
    "types/*.d.ts"
  ],
  "exclude": [
    "node_modules",
  ],
  "types": [
    "vue",
    "node",
    "webpack-env",
  ],
}
