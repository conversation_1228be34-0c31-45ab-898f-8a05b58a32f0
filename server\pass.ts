module.exports = function createPass(options) {
  const { publicPath } = options;

  const whitelist = [
    `^${publicPath}favicon.ico$`, // favicon图标，浏览器会自动访问该地址
    `^${publicPath}static/\\S+$`, // 静态文件目录
    '^/app.js$', // DEV生成文件
    '^/\\S+.hot-update.js(on)?$', // 热更新文件
    '^/__webpack_hmr$', // 热更新文件
  ];

  return (req) => {
    for (let i = 0; i < whitelist.length; i += 1) {
      if (new RegExp(whitelist[i], 'g').test(req.url)) {
        return true;
      }
    }

    return false;
  };
};
