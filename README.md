# 远程编辑器组件

## 安装

### 编译依赖

这个项目使用 [node](https://nodejs.org/) 和 [yarn](https://yarnpkg.com/)。请确保你本地安装了它们。

\- node 需要 [16.18.x](https://nodejs.org/en/download/) 或以上TLS版本（v17.x及以上未测试，不建议使用）

\- yarn 需要 [1.22.x](https://yarnpkg.com/getting-started/install) 或以上版本

### 服务器依赖

1. 运行系统

   在 CentOS 下运行 node v16 版本，需要手动安装特定工具，具体安装命令如下：

    `yum install centos-release-scl`

    `yum install devtoolset-7-gcc*`

    使用安装工具生效

    `source /opt/rh/devtoolset-7/enable` 或者 `scl enable devtoolset-7 bash`

2. 外部依赖服务项

   | 依赖服务 | 项目名称                                                     | 备注         |
   | -------- | ------------------------------------------------------------ | ------------ |
   | 静态资源 |                                                                 前端静态资源 |

   

## 运行示例（测试/开发使用）

1. 克隆代码，执行命令 ```<NAME_EMAIL>:pima/pima-remote-editor-vue3-nodejs.git```
2. 进入工程文件夹，执行命令 ```cd pima-remote-editor-vue3-nodejs```
3. 安装项目依赖，执行命令 ```yarn install --production=false```
4. 配置 [.env](#.env配置文件说明) 配置项
5. 启动示例，执行命令 ```yarn run dev```

## 构建（生产用）

1. 克隆代码，执行命令 ```<NAME_EMAIL>:pima/pima-remote-editor-vue3-nodejs.git```
2. 进入工程文件夹，执行命令 ```cd pima-remote-editor-vue3-nodejs```
3. 安装项目依赖，执行命令 ```yarn install --production=true```
4. 配置 [.env](#.env配置文件说明) 配置项
5. Webpack 构建，执行命令 ```yarn run build```

## 部署

### 配置 nginx

建议项，非必要。

由于构建完后，会生成 dist/ 文件夹，通过 nginx 配置，可直接访问相关资源。

找到 nginx 配置文件，具体修改如下：

```
location /remote-editor/remoteEntry.js {
  add_header 'Access-Control-Allow-Origin' '*';
  add_header 'Access-Control-Allow-Methods' 'GET';
  alias /home/<USER>/pima-remote-editor-nodejs/dist/remoteEntry.js;
}
```

```
location /remote-editor/static/ {
  add_header 'Access-Control-Allow-Origin' '*';
  add_header 'Access-Control-Allow-Methods' 'GET';
  alias /home/<USER>/pima-remote-editor-nodejs/dist/static/;
}
```

## .env配置文件说明

项目运行前需要配置 .env 文件，.env 文件不存在项目的版本管理系统(git)当中，需要单独创建，.env 配置文件需存放在项目根目录中。

项目提供配置对照文件 .env.sample，可复制该文件创建 .env 文件。

请注意，提供 .env 配置文件的目的，是为了不能修改直接系统环境变量的情况下，做为补充的配置手段。如果你已经在环境变量配置对应的项，则可以不用创建 .env 文件。

### 项目 .env 文件所需的配置选项

| 键值         | 备注               | 必填 | 默认值                          | 示例                     | 构建时使用 | 说明                          | 最后修改时间    |
| ------------ | ------------------ | ---- | ------------------------------- | ------------------------ | ---------- | ----------------------------- | --------------- |
| EXPRESS_PORT | Express 运行端口号 |      | 8080                            | 8080                     |            | 用于运行示例（测试/开发使用） | 2023/4/11 14:23 |
| SERVICE_URL  | 外网服务访问 URL   |      | http://localhost:{EXPRESS_PORT} | https://example.com:8080 |            | 用于运行示例（测试/开发使用） | 2023/4/11 14:23 |
| PUBLIC_PATH  | 根目录             |      | /                               | /test-folder/            | ✓          | 用于运行示例（测试/开发使用） | 2023/4/11 14:23 |

#### 接口相关配置

| 键值                      | 备注         | 必填 | 默认值 | 示例                                    | 构建时使用 | 说明                         | 最后修改时间    |
| ------------------------- | ------------ | ---- | ------ | --------------------------------------- | ---------- | ---------------------------- | --------------- |
| STATIC_RESOURCES_BASE_URL | 静态资源 URL | ✓    |        | https://pima.doocom.cn/static-resources | ✓          | 具体生产配置地址请找运维咨询 | 2023/4/11 14:23 |

