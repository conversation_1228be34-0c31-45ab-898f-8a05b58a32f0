import mammoth from 'mammoth/mammoth.browser';
import b64toBlob from 'b64-to-blob';


export function insertWordPlugin(editor, uiName) {
  const UploadState = Object.freeze({
    PENDING: 'pending',
    UPLOADING: 'uploading',
    DONE: 'done',
    ERROR: 'error',
  });
  const ATTR_ID = 'id';
  const ATTR_CLASS = 'class';
  const ATTR_SRC = 'src';
  const ATTR_ALT = 'alt';
  const ATTR_ORIG_SRC = 'data-orig-src';

  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const me = this;
  const imgElMap = new Map<string, string>();

  function setUploadState(imgId, uploadState) {
    imgElMap.set(imgId, uploadState);
  }

  function removeUploadState(imgId) {
    imgElMap.delete(imgId);
  }

  function createImgIdGenerator() {
    let imgId = 0;
    return function generateImgId() {
      imgId += 1;
      return `ueditor_iwl_${imgId}`;
    };
  }

  const generateImgId = createImgIdGenerator();

  function initImgTag(imgEl) {
    const imgId = generateImgId();
    imgEl.setAttribute(ATTR_ORIG_SRC, imgEl.src);
    imgEl.setAttribute(ATTR_ID, imgId);
    imgEl.setAttribute(ATTR_CLASS, 'loadingclass');
    imgEl.setAttribute(ATTR_SRC, `${me.options.themePath + me.options.theme}/images/spacer.gif`);
    return imgId;
  }

  function finishImgTag(imgEl, src) {
    imgEl.removeAttribute(ATTR_ORIG_SRC);
    imgEl.removeAttribute(ATTR_ID);
    imgEl.removeAttribute(ATTR_CLASS);
    imgEl.setAttribute(ATTR_SRC, src);
  }

  function initImagesUploadState(imgEls) {
    if (imgEls && imgEls.length) {
      for (let i = 0; i < imgEls.length; i += 1) {
        const imgEl = imgEls.item(i);
        const imgId = initImgTag(imgEl);
        setUploadState(imgId, UploadState.PENDING);
      }
    }
  }

  function getContentType(base64Data) {
    const reg = /^data:((\w|\/)+);/g;
    const matchs = reg.exec(base64Data);
    if (matchs && matchs.length && matchs.length > 1) {
      return matchs[1];
    }

    return null;
  }

  function getImageContent(base64Data) {
    return base64Data.substring(base64Data.indexOf('base64,') + 7);
  }

  function getImageFileExt(mime) {
    switch (mime) {
      case 'image/png':
        return 'png';
      default:
        return 'jpg';
    }
  }

  function sendFile(file, successHandler, errorHandler) {
    // 模拟数据
    // let fieldName;
    // let urlPrefix;
    // let maxSize;
    // let allowFiles;
    // let actionUrl;
    // let loadingHtml;
    // let errorHandler;
    // let successHandler;
    const filetype = 'image';
    // const loadingId = `loading_${(+new Date()).toString(36)}`;

    const fieldName = me.getOpt(`${filetype}FieldName`);
    // urlPrefix = me.getOpt(`${filetype}UrlPrefix`);
    // maxSize = me.getOpt(`${filetype}MaxSize`);
    // allowFiles = me.getOpt(`${filetype}AllowFiles`);
    const actionUrl = me.getActionUrl(me.getOpt(`${filetype}ActionName`));
    // errorHandler = function onError(title) {
    //   const loader = me.document.getElementById(loadingId);
    //   loader && domUtils.remove(loader);
    //   me.fireEvent('showmessage', {
    //     id: loadingId,
    //     content: title,
    //     type: 'error',
    //     timeout: 4000,
    //   });
    // };

    // loadingHtml = `<img class="loadingclass" id="${loadingId}" src="${
    //   me.options.themePath}${me.options.theme
    // }/images/spacer.gif" title="${me.getLang('autoupload.loading') || ''}" >`;
    // successHandler = function (data) {
    //   const link = urlPrefix + data.url;
    //   const loader = me.document.getElementById(loadingId);
    //   if (loader) {
    //     loader.setAttribute('src', link);
    //     loader.setAttribute('_src', link);
    //     loader.setAttribute('title', data.title || '');
    //     loader.setAttribute('alt', data.original || '');
    //     loader.removeAttribute('id');
    //     domUtils.removeClasses(loader, 'loadingclass');
    //   }
    // };

    // /* 插入loading的占位符 */
    // me.execCommand('inserthtml', loadingHtml);

    // /* 判断后端配置是否没有加载成功 */
    // if (!me.getOpt(`${filetype}ActionName`)) {
    //   errorHandler(me.getLang('autoupload.errorLoadConfig'));
    //   return;
    // }
    // /* 判断文件大小是否超出限制 */
    // if (file.size > maxSize) {
    //   errorHandler(me.getLang('autoupload.exceedSizeError'));
    //   return;
    // }
    // /* 判断文件格式是否超出允许 */
    // const fileext = file.name ? file.name.substr(file.name.lastIndexOf('.')) : '';
    // eslint-disable-next-line max-len
    // if ((fileext && filetype != 'image') || (allowFiles && (`${allowFiles.join('')}.`).indexOf(`${fileext.toLowerCase()}.`) == -1)) {
    //   errorHandler(me.getLang('autoupload.exceedTypeError'));
    //   return;
    // }

    /* 创建Ajax并提交 */
    const xhr = new XMLHttpRequest();
    const fd = new FormData();
    const params = window.UE.utils.serializeParam(me.queryCommandValue('serverparam')) || '';
    const url = window.UE.utils.formatUrl(actionUrl + (actionUrl.indexOf('?') === -1 ? '?' : '&') + params);

    fd.append(fieldName, file);
    fd.append('type', 'ajax');
    xhr.open('post', url, true);
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    // xhr.addEventListener('load', (ev: ProgressEvent<XMLHttpRequestEventTarget>) => {
    xhr.addEventListener('load', (ev) => {
      try {
        if (!(ev.target && 'response' in ev.target)) {
          return;
        }

        const res = ev.target.response as string;
        // eslint-disable-next-line no-new-func, @typescript-eslint/no-implied-eval
        const json = (new Function(`return ${window.UE.utils.trim(res)}`))();
        if (json.state === 'SUCCESS' && json.url) {
          successHandler(json);
        } else {
          errorHandler(json.state);
        }
      } catch (er) {
        errorHandler(me.getLang('autoupload.loadError'));
      }
    });
    xhr.send(fd);
  }

  function uploadImage(imgEl, successCB, failCB) {
    const fullBase64Data = imgEl.getAttribute(ATTR_ORIG_SRC);
    const contentType = getContentType(fullBase64Data);
    const imageContent = getImageContent(fullBase64Data);
    const blobData = b64toBlob(imageContent, contentType);
    const fileExt = getImageFileExt(contentType);
    const fileName = `${imgEl.getAttribute(ATTR_ALT) || imgEl.getAttribute(ATTR_ID)}.${fileExt}`;
    const file = new File([blobData], fileName, {
      type: contentType,
      lastModified: Date.now(),
    });

    sendFile(file, (data) => {
      successCB(data);
    }, (state) => {
      failCB(state);
    });
  }

  function uploadImages() {
    imgElMap.forEach((uploadState, imgId) => {
      if (UploadState.PENDING === uploadState) {
        const imgEl = me.document.getElementById(imgId);
        setUploadState(imgId, UploadState.UPLOADING);
        uploadImage(imgEl, (data) => {
          setUploadState(imgId, UploadState.DONE);
          finishImgTag(imgEl, data.url);
          removeUploadState(imgId);
        }, () => {
          setUploadState(imgId, UploadState.ERROR);
          removeUploadState(imgId);
        });
      }
    });
  }

  // eslint-disable-next-line prefer-const
  let isLoaded = false;

  function initInsertWordBtn(containerBtn) {
    const w = containerBtn.offsetWidth || 20;
    const h = containerBtn.offsetHeight || 20;
    const btnIframe = document.createElement('iframe');
    // eslint-disable-next-line max-len
    const btnStyle = `display:block;width:${w}px;height:${h}px;overflow:hidden;border:0;margin:0;padding:0;position:absolute;top:0;left:0;filter:alpha(opacity=0);-moz-opacity:0;-khtml-opacity: 0;opacity: 0;cursor:pointer;`;

    window.UE.dom.domUtils.on(btnIframe, 'load', () => {
      const timestrap = (+new Date()).toString(36);
      const btnIframeDoc = btnIframe.contentDocument || btnIframe.contentWindow.document;
      const btnIframeBody = btnIframeDoc.body;
      const wrapper = btnIframeDoc.createElement('div');

      wrapper.innerHTML = `<input
          id="edui_input_${timestrap}"
          type="file"
          accept=".docx"
          name="${me.options.imageFieldName}"
          style="${btnStyle}"
        >
        <iframe
          id="edui_iframe_${timestrap}"
          name="edui_iframe_${timestrap}"
          style="display:none;width:0;height:0;border:0;margin:0;padding:0;position:absolute;"
        ></iframe>`;

      wrapper.className = `edui-${me.options.theme}`;
      wrapper.id = `${me.ui.id}_iframeupload`;
      btnIframeBody.style.cssText = btnStyle;
      btnIframeBody.style.width = `${w}px`;
      btnIframeBody.style.height = `${h}px`;
      btnIframeBody.appendChild(wrapper);

      if (btnIframeBody.parentNode) {
        const pNode = btnIframeBody.parentNode as HTMLElement;
        pNode.style.width = `${w}px`;
        pNode.style.height = `${w}px`;
      }

      function readFileInputEventAsArrayBuffer(event, callback) {
        const file = event.target.files[0];
        const reader = new FileReader();
        reader.onload = function onLoad(loadEvent) {
          const arrayBuffer = loadEvent.target.result;
          callback(arrayBuffer);
        };

        reader.readAsArrayBuffer(file);
      }

      function handleFileSelect(event) {
        readFileInputEventAsArrayBuffer(event, (arrayBuffer) => {
          mammoth.convertToHtml({ arrayBuffer }).then((data) => {
            const doc = document.createElement('div');
            doc.innerHTML = unescape(data.value);

            // 找到所有的img，并设置上传前的状态
            const imgEls = doc.getElementsByTagName('img');
            initImagesUploadState(imgEls);

            // 将内容插入尾部
            me.setContent(doc.innerHTML, true);

            // 上传所有图片
            uploadImages();
          }).done();
        });
      }

      const input = btnIframeDoc.getElementById(`edui_input_${timestrap}`) as HTMLInputElement;
      window.UE.dom.domUtils.on(input, 'change', (event) => {
        if (!input.value) return;
        handleFileSelect(event);
        input.value = '';
      });

      let stateTimer;
      me.addListener('selectionchange', () => {
        clearTimeout(stateTimer);
        stateTimer = setTimeout(() => {
          const state = me.queryCommandState('insertword');
          if (state === -1) {
            input.disabled = true;
          } else {
            input.disabled = false;
          }
        }, 400);
      });
      isLoaded = true;
    });

    btnIframe.style.cssText = btnStyle;
    containerBtn.appendChild(btnIframe);
  }

  // 注册按钮执行时的command命令,用uiName作为command名字，使用命令默认就会带有回退操作
  editor.registerCommand(uiName, {
    queryCommandState() {
      return isLoaded ? 0 : -1;
    },
  });

  // 创建下拉菜单中的键值对，这里我用字体大小作为例子
  // 创建下来框
  const button = new window.UE.ui.Button({
    // 需要指定当前的编辑器实例
    editor,
    // 提示
    title: uiName,
    name: uiName,
    cssRules: 'background-position: -300px -40px',
    onclick() {
      // 这里可以不用执行命令,做你自己的操作也可
      editor.execCommand(uiName);
    },
  });

  editor.addListener('ready', () => {
    const buttonBody = button.getDom('body');
    const iconSpan = buttonBody.children[0];
    initInsertWordBtn(iconSpan);
  });

  editor.addListener('selectionchange', (type, causeByUi, uiReady) => {
    if (!uiReady) {
      const state = editor.queryCommandState(uiName);
      if (state === -1) {
        button.setDisabled(true);
      } else {
        button.setDisabled(false);
      }
    }
  });

  return button;
}
