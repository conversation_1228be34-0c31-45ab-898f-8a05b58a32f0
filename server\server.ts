// @ts-expect-error: Cannot redeclare block-scoped variable 'process'.ts(2451)
const process = require('process');
const express = require('express');
const path = require('path');
const url = require('url');
const logger = require('morgan');
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');
const session = require('express-session');

const readEnvConfig = require('./env-config');

readEnvConfig();

const createRedisClient = require('./redis-client');
const createRedisStore = require('./redis-store');
const createPass = require('./pass');
const createConfig = require('./config');
const createPublicPathFixMiddleware = require('./middlewares/public-path-fix');
const createUnlessMiddleware = require('./middlewares/unless');
const createVueSSRMiddleware = require('./middlewares/vue-ssr');


const config = createConfig();
const {
  COOKIE_SECRET,
  REDIS_KEY_PREFIX,
  REDIS_TTL,
  SESSION_ID_COOKIE_KEY,
  PUBLIC_PATH,
  HOT_CLIENT_WEB_SOCKET_PORT,
} = config;

const pass = createPass({ publicPath: PUBLIC_PATH });

const isProd = process.env.NODE_ENV === 'production';
const expressPort = process.env.EXPRESS_PORT || 8080;
const localhostUrl = expressPort === 80 ? 'http://localhost' : `http://localhost:${expressPort}`;
const serviceUrlString = process.env.SERVICE_URL || localhostUrl;
const serviceUrl = new url.URL(serviceUrlString);
const cookieSecure = serviceUrl.protocol === 'https:';

function resolve(...args) {
  return path.resolve(__dirname, ...args);
}

// 当收到未捕获的异常时，退出进程
process.on('uncaughtException', (error) => {
  // 记录未捕获的异常错误日志
  // eslint-disable-next-line no-console
  console.error('UncaughtException', error);
  // 手动退出进程，pm2会自动重启
  process.exit(1);
});

const app = express();
// 如果使用HTTPS，需要设置X-Forwarded-Proto头，防止express-session中的Cookie解密失败
if (cookieSecure) {
  app.set('trust proxy', 1); // trust first proxy
  app.use((req, res, next) => {
    // eslint-disable-next-line no-param-reassign
    req.headers['x-forwarded-proto'] = 'https';
    next();
  });
}

app.use(logger('combined'));


const staticOptions = {};
if (isProd) {
  app.use(
    `${PUBLIC_PATH}static/`,
    express.static(resolve('../dist/static/'), staticOptions),
  );
} else {
  app.use(express.static(resolve('../src/assets'), staticOptions));
}

app.use(cookieParser(COOKIE_SECRET));
app.use(bodyParser.urlencoded({ limit: '2gb', extended: true }));
app.use(bodyParser.json({ limit: '2gb' }));


// 修复根问题路径问题，如果访问地址为不带 / 的，自动重定向至带 / 的地址
const publicPathFixMiddleware = createPublicPathFixMiddleware({
  originPublicPath: PUBLIC_PATH,
});
app.get('*', publicPathFixMiddleware);

// Redis
const initRedisClientOptions = {
  redisKeyPrefix: REDIS_KEY_PREFIX,
};
const redisClient = createRedisClient(initRedisClientOptions);

const initRedisStoreOptions = {
  redisClient,
  redisTtl: REDIS_TTL,
};
const redisStore = createRedisStore(initRedisStoreOptions);

const sessionOptions = {
  proxy: true,
  store: redisStore,
  secret: COOKIE_SECRET,
  resave: false,
  saveUninitialized: false,
  name: SESSION_ID_COOKIE_KEY,
  cookie: {
    secure: cookieSecure,
    httpOnly: true,
    sameSite: 'lax', // 暂时不能使用strict https://bugzilla.mozilla.org/show_bug.cgi?id=1465402
  },
};
app.use(session(sessionOptions));


// vue-ssr中间件
const vueSSRMiddleware = createVueSSRMiddleware(app, {
  isProd,
  publicPath: PUBLIC_PATH,
  webpackClientConfig: isProd
    ? require('../build/webpack.client.production')
    : require('../build/webpack.client.development'),
  webpackServerConfig: isProd
    ? require('../build/webpack.server.production')
    : require('../build/webpack.server.development'),
  hotClientWebSocketPort: HOT_CLIENT_WEB_SOCKET_PORT,
  templatePath: resolve('../index.template.html'),
  vueSSRServerBundlePath: resolve('../dist/vue-ssr-server-bundle.json'),
  vueSSRClientManifestPath: resolve('../dist/vue-ssr-client-manifest.json'),
});
app.get('*', createUnlessMiddleware(pass, vueSSRMiddleware));

// 错误处理
app.use((error, req, res, next) => {
  // 以下三种情况不需要处理
  // 1、没有出错
  // 2、AJAX请求
  // 3、不是GET请求
  if (!error || req.xhr || req.method.toLowerCase() !== 'get') {
    next();
    return;
  }

  // eslint-disable-next-line no-console
  console.error(new Date().toISOString(), 'errorHandler', error);
  res.status(500).send('Internal server error').end();
});

app.on('error', (err) => {
  // eslint-disable-next-line no-console
  console.error(
    new Date().toISOString(),
    'Server error: \n%s\n%s ',
    err.stack || '',
  );
});

app.listen(expressPort, () => {
  // eslint-disable-next-line no-console
  console.log(`Express server listen in ${expressPort}`);
});
